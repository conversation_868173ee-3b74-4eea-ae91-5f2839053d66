<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Discover amazing restaurants in your city. Browse by cuisine, rating, delivery time and more. Order your favorite food online with Foodie.">
  <meta name="keywords" content="restaurants, food delivery, online ordering, cuisines, food near me">
  <meta name="author" content="Foodie">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://foodie.com/restaurants">
  <meta property="og:title" content="Restaurants - Foodie">
  <meta property="og:description" content="Discover amazing restaurants in your city. Browse by cuisine, rating, delivery time and more.">
  <meta property="og:image" content="https://foodie.com/og-restaurants.jpg">

  <title>Restaurants - Foodie | Discover Amazing Food</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/assets/icons/favicon.ico">
  
  <!-- Stylesheets -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
  
  <!-- Custom Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#fef2f2', 100: '#fee2e2', 200: '#fecaca', 300: '#fca5a5', 400: '#f87171',
              500: '#ef4444', 600: '#dc2626', 700: '#b91c1c', 800: '#991b1b', 900: '#7f1d1d'
            },
            secondary: {
              50: '#f8fafc', 100: '#f1f5f9', 200: '#e2e8f0', 300: '#cbd5e1', 400: '#94a3b8',
              500: '#64748b', 600: '#475569', 700: '#334155', 800: '#1e293b', 900: '#0f172a'
            },
            success: {
              50: '#f0fdf4', 100: '#dcfce7', 200: '#bbf7d0', 300: '#86efac', 400: '#4ade80',
              500: '#22c55e', 600: '#16a34a', 700: '#15803d', 800: '#166534', 900: '#14532d'
            },
            warning: {
              50: '#fffbeb', 100: '#fef3c7', 200: '#fde68a', 300: '#fcd34d', 400: '#fbbf24',
              500: '#f59e0b', 600: '#d97706', 700: '#b45309', 800: '#92400e', 900: '#78350f'
            }
          },
          fontFamily: {
            'sans': ['Inter', 'system-ui', 'sans-serif'],
            'display': ['Poppins', 'system-ui', 'sans-serif']
          }
        }
      }
    }
  </script>
  
  <!-- Custom CSS -->
  <style>
    .btn { @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed; }
    .btn-primary { @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-sm hover:shadow-md; }
    .btn-secondary { @apply btn bg-white text-secondary-700 border border-secondary-300 hover:bg-secondary-50 focus:ring-secondary-500 shadow-sm hover:shadow-md; }
    .btn-ghost { @apply btn bg-transparent text-secondary-600 hover:bg-secondary-100 focus:ring-secondary-500; }
    .btn-sm { @apply px-3 py-1.5 text-xs; }
    .card { @apply bg-white rounded-xl shadow-sm border border-secondary-100 overflow-hidden; }
    .card-hover { @apply card transition-all duration-300 hover:shadow-lg hover:-translate-y-1; }
    .form-input { @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200; }
    .container-custom { @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8; }
    .bg-gradient-primary { @apply bg-gradient-to-r from-primary-500 to-primary-600; }
    .skeleton { @apply animate-pulse bg-secondary-200 rounded; }
  </style>
</head>

<body class="font-sans text-secondary-800 antialiased bg-secondary-50">
  <!-- Header -->
  <header class="bg-white shadow-sm border-b border-secondary-100 sticky top-0 z-40" x-data="{ mobileMenuOpen: false }">
    <div class="container-custom">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <a href="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
            <span class="text-xl font-display font-bold text-secondary-900">Foodie</span>
          </a>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <a href="/" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors">Home</a>
          <a href="/pages/restaurants.html" class="text-primary-600 font-medium">Restaurants</a>
          <div class="relative" x-data="{ open: false }">
            <button @click="open = !open" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors flex items-center">
              Categories
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div x-show="open" @click.away="open = false" x-transition class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-100 py-2">
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Pizza</a>
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Burgers</a>
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Indian</a>
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Chinese</a>
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Italian</a>
            </div>
          </div>
          <a href="#" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors">About</a>
        </nav>

        <!-- Desktop Actions -->
        <div class="hidden md:flex items-center space-x-4">
          <!-- Cart -->
          <a href="/pages/cart.html" class="relative p-2 text-secondary-600 hover:text-primary-600 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
            </svg>
            <span class="cart-count absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium" style="display: none;">0</span>
          </a>

          <!-- User Menu -->
          <div class="relative" x-data="{ userMenuOpen: false }">
            <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-2 p-2 text-secondary-600 hover:text-primary-600 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </button>
            <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-100 py-2">
              <a href="/pages/login.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Sign In</a>
              <a href="/pages/signup.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Sign Up</a>
              <a href="/pages/account.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">My Account</a>
            </div>
          </div>
        </div>

        <!-- Mobile menu button -->
        <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 text-secondary-600 hover:text-primary-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <div x-show="mobileMenuOpen" x-transition class="md:hidden border-t border-secondary-200 py-4">
        <nav class="space-y-2">
          <a href="/" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Home</a>
          <a href="/pages/restaurants.html" class="block px-4 py-2 text-primary-600 bg-primary-50 rounded-lg font-medium">Restaurants</a>
          <a href="#" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Categories</a>
          <a href="#" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">About</a>
          <hr class="my-2 border-secondary-200">
          <a href="/pages/cart.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
            </svg>
            Cart
            <span class="cart-count ml-auto bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium" style="display: none;">0</span>
          </a>
          <a href="/pages/login.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Sign In</a>
          <a href="/pages/signup.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Sign Up</a>
        </nav>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="py-8">
    <div class="container-custom">
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-3xl lg:text-4xl font-display font-bold text-secondary-900 mb-2">
          Restaurants Near You
        </h1>
        <p class="text-lg text-secondary-600">
          Discover amazing restaurants and order your favorite food
        </p>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white rounded-xl shadow-sm border border-secondary-100 p-6 mb-8" 
           x-data="{ 
             searchQuery: '', 
             selectedCuisine: '', 
             selectedRating: '', 
             selectedDeliveryTime: '',
             sortBy: 'popularity',
             viewMode: 'grid',
             showFilters: false
           }">
        
        <!-- Search Bar -->
        <div class="flex flex-col lg:flex-row gap-4 mb-6">
          <div class="flex-1 relative">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input 
              type="text" 
              x-model="searchQuery"
              placeholder="Search restaurants, cuisines, or dishes..." 
              class="form-input pl-10 w-full"
            >
          </div>
          <div class="flex gap-2">
            <button @click="showFilters = !showFilters" class="btn-secondary lg:hidden">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.828a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
              </svg>
              Filters
            </button>
            <button class="btn-primary">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              Search
            </button>
          </div>
        </div>

        <!-- Filters -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4" :class="{ 'hidden lg:grid': !showFilters }">
          <!-- Cuisine Filter -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">Cuisine</label>
            <select x-model="selectedCuisine" class="form-input">
              <option value="">All Cuisines</option>
              <option value="pizza">Pizza</option>
              <option value="burgers">Burgers</option>
              <option value="indian">Indian</option>
              <option value="chinese">Chinese</option>
              <option value="italian">Italian</option>
              <option value="mexican">Mexican</option>
            </select>
          </div>

          <!-- Rating Filter -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">Rating</label>
            <select x-model="selectedRating" class="form-input">
              <option value="">Any Rating</option>
              <option value="4.5">4.5+ Stars</option>
              <option value="4.0">4.0+ Stars</option>
              <option value="3.5">3.5+ Stars</option>
              <option value="3.0">3.0+ Stars</option>
            </select>
          </div>

          <!-- Delivery Time Filter -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">Delivery Time</label>
            <select x-model="selectedDeliveryTime" class="form-input">
              <option value="">Any Time</option>
              <option value="15">Under 15 min</option>
              <option value="30">Under 30 min</option>
              <option value="45">Under 45 min</option>
              <option value="60">Under 1 hour</option>
            </select>
          </div>

          <!-- Sort By -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">Sort By</label>
            <select x-model="sortBy" class="form-input">
              <option value="popularity">Popularity</option>
              <option value="rating">Rating</option>
              <option value="delivery_time">Delivery Time</option>
              <option value="cost_low_to_high">Cost: Low to High</option>
              <option value="cost_high_to_low">Cost: High to Low</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Results Header -->
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6"
           x-data="{ viewMode: 'grid' }">
        <div>
          <p class="text-secondary-600">
            <span class="font-medium text-secondary-900">247 restaurants</span> found
          </p>
        </div>

        <!-- View Toggle -->
        <div class="flex items-center space-x-4">
          <div class="flex bg-secondary-100 rounded-lg p-1">
            <button
              @click="viewMode = 'grid'"
              :class="viewMode === 'grid' ? 'bg-white shadow-sm' : ''"
              class="p-2 rounded-md transition-all duration-200"
            >
              <svg class="w-4 h-4" :class="viewMode === 'grid' ? 'text-primary-600' : 'text-secondary-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
              </svg>
            </button>
            <button
              @click="viewMode = 'list'"
              :class="viewMode === 'list' ? 'bg-white shadow-sm' : ''"
              class="p-2 rounded-md transition-all duration-200"
            >
              <svg class="w-4 h-4" :class="viewMode === 'list' ? 'text-primary-600' : 'text-secondary-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Restaurant Grid -->
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8" x-show="viewMode === 'grid'">
        <!-- Restaurant Card 1 -->
        <div class="card-hover">
          <div class="relative">
            <img
              src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
              alt="Pizza Palace"
              class="w-full h-48 object-cover"
              loading="lazy"
            >
            <div class="absolute top-4 left-4">
              <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                ⭐ 4.5
              </span>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                25-30 min
              </span>
            </div>
            <div class="absolute bottom-4 left-4">
              <span class="bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                50% OFF
              </span>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-semibold text-secondary-900 mb-2">Pizza Palace</h3>
            <p class="text-secondary-600 mb-3">Italian • Pizza • Fast Food</p>
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm text-secondary-500">₹200 for two</span>
              <span class="text-xs text-success-600 font-medium">Free delivery</span>
            </div>
            <button class="btn-primary w-full" onclick="window.location.href='/pages/restaurant.html'">
              View Menu
            </button>
          </div>
        </div>

        <!-- Restaurant Card 2 -->
        <div class="card-hover">
          <div class="relative">
            <img
              src="https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
              alt="Burger Barn"
              class="w-full h-48 object-cover"
              loading="lazy"
            >
            <div class="absolute top-4 left-4">
              <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                ⭐ 4.2
              </span>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                20-25 min
              </span>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-semibold text-secondary-900 mb-2">Burger Barn</h3>
            <p class="text-secondary-600 mb-3">American • Burgers • Fast Food</p>
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm text-secondary-500">₹300 for two</span>
              <span class="text-xs text-secondary-500">₹40 delivery fee</span>
            </div>
            <button class="btn-primary w-full" onclick="window.location.href='/pages/restaurant.html'">
              View Menu
            </button>
          </div>
        </div>

        <!-- Restaurant Card 3 -->
        <div class="card-hover">
          <div class="relative">
            <img
              src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
              alt="Spice Garden"
              class="w-full h-48 object-cover"
              loading="lazy"
            >
            <div class="absolute top-4 left-4">
              <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                ⭐ 4.7
              </span>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                35-40 min
              </span>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-semibold text-secondary-900 mb-2">Spice Garden</h3>
            <p class="text-secondary-600 mb-3">Indian • North Indian • Biryani</p>
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm text-secondary-500">₹400 for two</span>
              <span class="text-xs text-success-600 font-medium">Free delivery</span>
            </div>
            <button class="btn-primary w-full" onclick="window.location.href='/pages/restaurant.html'">
              View Menu
            </button>
          </div>
        </div>

        <!-- Restaurant Card 4 -->
        <div class="card-hover">
          <div class="relative">
            <img
              src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
              alt="Sushi Zen"
              class="w-full h-48 object-cover"
              loading="lazy"
            >
            <div class="absolute top-4 left-4">
              <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                ⭐ 4.8
              </span>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                45-50 min
              </span>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-semibold text-secondary-900 mb-2">Sushi Zen</h3>
            <p class="text-secondary-600 mb-3">Japanese • Sushi • Asian</p>
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm text-secondary-500">₹800 for two</span>
              <span class="text-xs text-secondary-500">₹60 delivery fee</span>
            </div>
            <button class="btn-primary w-full" onclick="window.location.href='/pages/restaurant.html'">
              View Menu
            </button>
          </div>
        </div>

        <!-- Restaurant Card 5 -->
        <div class="card-hover">
          <div class="relative">
            <img
              src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
              alt="Taco Fiesta"
              class="w-full h-48 object-cover"
              loading="lazy"
            >
            <div class="absolute top-4 left-4">
              <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                ⭐ 4.3
              </span>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                30-35 min
              </span>
            </div>
            <div class="absolute bottom-4 left-4">
              <span class="bg-warning-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                NEW
              </span>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-semibold text-secondary-900 mb-2">Taco Fiesta</h3>
            <p class="text-secondary-600 mb-3">Mexican • Tacos • Tex-Mex</p>
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm text-secondary-500">₹350 for two</span>
              <span class="text-xs text-success-600 font-medium">Free delivery</span>
            </div>
            <button class="btn-primary w-full" onclick="window.location.href='/pages/restaurant.html'">
              View Menu
            </button>
          </div>
        </div>

        <!-- Restaurant Card 6 -->
        <div class="card-hover">
          <div class="relative">
            <img
              src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
              alt="Pasta Corner"
              class="w-full h-48 object-cover"
              loading="lazy"
            >
            <div class="absolute top-4 left-4">
              <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                ⭐ 4.4
              </span>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                25-30 min
              </span>
            </div>
          </div>
          <div class="p-6">
            <h3 class="text-xl font-semibold text-secondary-900 mb-2">Pasta Corner</h3>
            <p class="text-secondary-600 mb-3">Italian • Pasta • Continental</p>
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm text-secondary-500">₹450 for two</span>
              <span class="text-xs text-secondary-500">₹30 delivery fee</span>
            </div>
            <button class="btn-primary w-full" onclick="window.location.href='/pages/restaurant.html'">
              View Menu
            </button>
          </div>
        </div>
      </div>

      <!-- Restaurant List View -->
      <div class="space-y-4 mb-8" x-show="viewMode === 'list'">
        <!-- Restaurant List Item 1 -->
        <div class="card-hover">
          <div class="flex flex-col md:flex-row">
            <div class="relative md:w-64 flex-shrink-0">
              <img
                src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Pizza Palace"
                class="w-full h-48 md:h-full object-cover"
                loading="lazy"
              >
              <div class="absolute top-4 left-4">
                <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  ⭐ 4.5
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span class="bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  50% OFF
                </span>
              </div>
            </div>
            <div class="flex-1 p-6">
              <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                <div class="flex-1">
                  <h3 class="text-xl font-semibold text-secondary-900 mb-2">Pizza Palace</h3>
                  <p class="text-secondary-600 mb-3">Italian • Pizza • Fast Food</p>
                  <div class="flex items-center space-x-4 text-sm text-secondary-500 mb-4">
                    <span>₹200 for two</span>
                    <span>•</span>
                    <span>25-30 min</span>
                    <span>•</span>
                    <span class="text-success-600 font-medium">Free delivery</span>
                  </div>
                  <p class="text-sm text-secondary-600 leading-relaxed">
                    Authentic Italian pizzas with fresh ingredients and traditional recipes. Known for their wood-fired oven and crispy crusts.
                  </p>
                </div>
                <div class="mt-4 md:mt-0 md:ml-6 flex-shrink-0">
                  <button class="btn-primary" onclick="window.location.href='/pages/restaurant.html'">
                    View Menu
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Restaurant List Item 2 -->
        <div class="card-hover">
          <div class="flex flex-col md:flex-row">
            <div class="relative md:w-64 flex-shrink-0">
              <img
                src="https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Burger Barn"
                class="w-full h-48 md:h-full object-cover"
                loading="lazy"
              >
              <div class="absolute top-4 left-4">
                <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  ⭐ 4.2
                </span>
              </div>
            </div>
            <div class="flex-1 p-6">
              <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                <div class="flex-1">
                  <h3 class="text-xl font-semibold text-secondary-900 mb-2">Burger Barn</h3>
                  <p class="text-secondary-600 mb-3">American • Burgers • Fast Food</p>
                  <div class="flex items-center space-x-4 text-sm text-secondary-500 mb-4">
                    <span>₹300 for two</span>
                    <span>•</span>
                    <span>20-25 min</span>
                    <span>•</span>
                    <span>₹40 delivery fee</span>
                  </div>
                  <p class="text-sm text-secondary-600 leading-relaxed">
                    Juicy burgers made with premium beef patties and fresh vegetables. Famous for their signature sauces and crispy fries.
                  </p>
                </div>
                <div class="mt-4 md:mt-0 md:ml-6 flex-shrink-0">
                  <button class="btn-primary" onclick="window.location.href='/pages/restaurant.html'">
                    View Menu
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Restaurant List Item 3 -->
        <div class="card-hover">
          <div class="flex flex-col md:flex-row">
            <div class="relative md:w-64 flex-shrink-0">
              <img
                src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                alt="Spice Garden"
                class="w-full h-48 md:h-full object-cover"
                loading="lazy"
              >
              <div class="absolute top-4 left-4">
                <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  ⭐ 4.7
                </span>
              </div>
            </div>
            <div class="flex-1 p-6">
              <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                <div class="flex-1">
                  <h3 class="text-xl font-semibold text-secondary-900 mb-2">Spice Garden</h3>
                  <p class="text-secondary-600 mb-3">Indian • North Indian • Biryani</p>
                  <div class="flex items-center space-x-4 text-sm text-secondary-500 mb-4">
                    <span>₹400 for two</span>
                    <span>•</span>
                    <span>35-40 min</span>
                    <span>•</span>
                    <span class="text-success-600 font-medium">Free delivery</span>
                  </div>
                  <p class="text-sm text-secondary-600 leading-relaxed">
                    Authentic Indian cuisine with aromatic spices and traditional cooking methods. Specializes in biryanis and North Indian curries.
                  </p>
                </div>
                <div class="mt-4 md:mt-0 md:ml-6 flex-shrink-0">
                  <button class="btn-primary" onclick="window.location.href='/pages/restaurant.html'">
                    View Menu
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div class="flex flex-col sm:flex-row justify-between items-center gap-4 mb-8">
        <p class="text-sm text-secondary-600">
          Showing <span class="font-medium">1-12</span> of <span class="font-medium">247</span> restaurants
        </p>

        <nav class="flex items-center space-x-2" aria-label="Pagination">
          <button class="btn-ghost btn-sm" disabled>
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Previous
          </button>

          <div class="flex items-center space-x-1">
            <button class="w-8 h-8 flex items-center justify-center text-sm font-medium bg-primary-500 text-white rounded-lg">1</button>
            <button class="w-8 h-8 flex items-center justify-center text-sm font-medium text-secondary-600 hover:bg-secondary-100 rounded-lg">2</button>
            <button class="w-8 h-8 flex items-center justify-center text-sm font-medium text-secondary-600 hover:bg-secondary-100 rounded-lg">3</button>
            <span class="px-2 text-secondary-400">...</span>
            <button class="w-8 h-8 flex items-center justify-center text-sm font-medium text-secondary-600 hover:bg-secondary-100 rounded-lg">21</button>
          </div>

          <button class="btn-ghost btn-sm">
            Next
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </nav>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-secondary-900 text-white">
    <div class="container-custom py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
            <span class="text-xl font-display font-bold">Foodie</span>
          </div>
          <p class="text-secondary-300 text-sm leading-relaxed">
            Discover and order from the best restaurants in your city. Fast delivery, great food, amazing experience.
          </p>
        </div>

        <!-- Quick Links -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Quick Links</h3>
          <ul class="space-y-2">
            <li><a href="/" class="text-secondary-300 hover:text-white transition-colors text-sm">Home</a></li>
            <li><a href="/pages/restaurants.html" class="text-secondary-300 hover:text-white transition-colors text-sm">Restaurants</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">About Us</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Contact</a></li>
          </ul>
        </div>

        <!-- Categories -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Popular Cuisines</h3>
          <ul class="space-y-2">
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Pizza</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Burgers</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Indian</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Chinese</a></li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Get in Touch</h3>
          <div class="space-y-3">
            <div class="flex items-center space-x-3">
              <svg class="w-5 h-5 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
              </svg>
              <p class="text-secondary-300 text-sm">+1 (555) 123-4567</p>
            </div>
            <div class="flex items-center space-x-3">
              <svg class="w-5 h-5 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
              <p class="text-secondary-300 text-sm"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="border-t border-secondary-800 mt-12 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p class="text-secondary-400 text-sm">
            © 2024 Foodie. All rights reserved.
          </p>
          <div class="flex space-x-6">
            <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
            <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Terms of Service</a>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // Initialize cart count
    document.addEventListener('DOMContentLoaded', function() {
      updateCartCount();
    });

    // Shopping cart functionality
    let cart = JSON.parse(localStorage.getItem('cart')) || [];

    function updateCartCount() {
      const count = cart.reduce((total, item) => total + item.quantity, 0);
      const cartCountElements = document.querySelectorAll('.cart-count');

      cartCountElements.forEach(element => {
        element.textContent = count;
        element.style.display = count > 0 ? 'inline-flex' : 'none';
      });
    }

    function addToCart(item) {
      const existingItem = cart.find(cartItem => cartItem.id === item.id);

      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        cart.push({ ...item, quantity: 1 });
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      updateCartCount();
      showToast(`${item.name} added to cart!`);
    }

    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500'
      }`;
      toast.textContent = message;

      document.body.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove('translate-x-full');
      }, 100);

      setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 3000);
    }

    // Search functionality
    function performSearch() {
      const searchQuery = document.querySelector('input[x-model="searchQuery"]').value;
      const selectedCuisine = document.querySelector('select[x-model="selectedCuisine"]').value;
      const selectedRating = document.querySelector('select[x-model="selectedRating"]').value;
      const selectedDeliveryTime = document.querySelector('select[x-model="selectedDeliveryTime"]').value;
      const sortBy = document.querySelector('select[x-model="sortBy"]').value;

      console.log('Search parameters:', {
        searchQuery,
        selectedCuisine,
        selectedRating,
        selectedDeliveryTime,
        sortBy
      });

      // In a real application, this would make an API call to filter restaurants
      showToast('Search functionality would be implemented with backend API');
    }
  </script>
</body>
</html>
