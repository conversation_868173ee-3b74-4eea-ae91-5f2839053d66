// Main JavaScript file for Foodie Delivery Website
// Using Alpine.js for reactive components and vanilla JS for utilities

import Alpine from 'alpinejs'

// Initialize Alpine.js
window.Alpine = Alpine
Alpine.start()

// Utility functions
class Utils {
  // Smooth scroll to element
  static scrollTo(elementId) {
    const element = document.getElementById(elementId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // Format currency
  static formatCurrency(amount) {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  // Format rating
  static formatRating(rating) {
    return Math.round(rating * 10) / 10
  }

  // Debounce function for search
  static debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  // Show toast notification
  static showToast(message, type = 'success') {
    const toast = document.createElement('div')
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300 ${
      type === 'success' ? 'bg-success-500' : type === 'error' ? 'bg-red-500' : 'bg-primary-500'
    }`
    toast.textContent = message
    
    document.body.appendChild(toast)
    
    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full')
    }, 100)
    
    // Remove after 3 seconds
    setTimeout(() => {
      toast.classList.add('translate-x-full')
      setTimeout(() => {
        document.body.removeChild(toast)
      }, 300)
    }, 3000)
  }

  // Local storage helpers
  static getFromStorage(key) {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('Error reading from localStorage:', error)
      return null
    }
  }

  static setToStorage(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Error writing to localStorage:', error)
    }
  }
}

// Shopping Cart functionality
class ShoppingCart {
  constructor() {
    this.items = Utils.getFromStorage('cart') || []
    this.updateCartCount()
  }

  addItem(item) {
    const existingItem = this.items.find(cartItem => cartItem.id === item.id)
    
    if (existingItem) {
      existingItem.quantity += item.quantity || 1
    } else {
      this.items.push({ ...item, quantity: item.quantity || 1 })
    }
    
    this.saveCart()
    this.updateCartCount()
    Utils.showToast(`${item.name} added to cart!`)
  }

  removeItem(itemId) {
    this.items = this.items.filter(item => item.id !== itemId)
    this.saveCart()
    this.updateCartCount()
  }

  updateQuantity(itemId, quantity) {
    const item = this.items.find(cartItem => cartItem.id === itemId)
    if (item) {
      if (quantity <= 0) {
        this.removeItem(itemId)
      } else {
        item.quantity = quantity
        this.saveCart()
        this.updateCartCount()
      }
    }
  }

  getTotal() {
    return this.items.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  getItemCount() {
    return this.items.reduce((count, item) => count + item.quantity, 0)
  }

  clear() {
    this.items = []
    this.saveCart()
    this.updateCartCount()
  }

  saveCart() {
    Utils.setToStorage('cart', this.items)
  }

  updateCartCount() {
    const cartCountElements = document.querySelectorAll('.cart-count')
    const count = this.getItemCount()
    
    cartCountElements.forEach(element => {
      element.textContent = count
      element.style.display = count > 0 ? 'inline-flex' : 'none'
    })
  }
}

// Initialize shopping cart
window.cart = new ShoppingCart()

// Scroll animations
class ScrollAnimations {
  constructor() {
    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible')
          }
        })
      },
      { threshold: 0.1, rootMargin: '0px 0px -50px 0px' }
    )
    
    this.init()
  }

  init() {
    const animatedElements = document.querySelectorAll('.animate-on-scroll')
    animatedElements.forEach(el => this.observer.observe(el))
  }
}

// Initialize scroll animations when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ScrollAnimations()
})

// Search functionality
class Search {
  constructor() {
    this.debouncedSearch = Utils.debounce(this.performSearch.bind(this), 300)
  }

  performSearch(query, type = 'restaurants') {
    // This would typically make an API call
    console.log(`Searching for: ${query} in ${type}`)
    
    // Simulate search results
    const results = this.mockSearchResults(query, type)
    this.displayResults(results, type)
  }

  mockSearchResults(query, type) {
    // Mock data - in a real app, this would come from an API
    const mockData = {
      restaurants: [
        { id: 1, name: 'Pizza Palace', cuisine: 'Italian', rating: 4.5, image: 'pizza-palace.jpg' },
        { id: 2, name: 'Burger Barn', cuisine: 'American', rating: 4.2, image: 'burger-barn.jpg' },
        { id: 3, name: 'Sushi Zen', cuisine: 'Japanese', rating: 4.8, image: 'sushi-zen.jpg' }
      ]
    }
    
    return mockData[type]?.filter(item => 
      item.name.toLowerCase().includes(query.toLowerCase()) ||
      item.cuisine.toLowerCase().includes(query.toLowerCase())
    ) || []
  }

  displayResults(results, type) {
    const resultsContainer = document.getElementById('search-results')
    if (!resultsContainer) return

    if (results.length === 0) {
      resultsContainer.innerHTML = '<p class="text-secondary-500 text-center py-8">No results found</p>'
      return
    }

    resultsContainer.innerHTML = results.map(item => `
      <div class="card-hover p-4">
        <h3 class="font-semibold text-lg">${item.name}</h3>
        <p class="text-secondary-600">${item.cuisine}</p>
        <div class="flex items-center mt-2">
          <span class="text-warning-500">★</span>
          <span class="ml-1 text-sm">${item.rating}</span>
        </div>
      </div>
    `).join('')
  }
}

// Initialize search
window.search = new Search()

// Export utilities for global use
window.Utils = Utils

// Mobile menu toggle
document.addEventListener('DOMContentLoaded', () => {
  const mobileMenuButton = document.getElementById('mobile-menu-button')
  const mobileMenu = document.getElementById('mobile-menu')
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden')
    })
  }
})
