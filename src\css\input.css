@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-secondary-800 antialiased;
  }
  
  /* Focus styles for accessibility */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400;
  }
}

/* Component styles */
@layer components {
  /* Button components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }
  
  .btn-secondary {
    @apply btn bg-white text-secondary-700 border border-secondary-300 hover:bg-secondary-50 focus:ring-secondary-500 shadow-sm hover:shadow-md;
  }
  
  .btn-outline {
    @apply btn bg-transparent text-primary-600 border border-primary-500 hover:bg-primary-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent text-secondary-600 hover:bg-secondary-100 focus:ring-secondary-500;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  /* Card components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-secondary-100 overflow-hidden;
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
  }
  
  /* Form components */
  .form-input {
    @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-secondary-700 mb-1;
  }
  
  .form-error {
    @apply text-sm text-red-600 mt-1;
  }
  
  /* Badge components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  /* Rating component */
  .rating {
    @apply inline-flex items-center;
  }
  
  .rating-star {
    @apply w-4 h-4 text-warning-400 fill-current;
  }
  
  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-secondary-200 rounded;
  }
  
  /* Utility classes */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Animation utilities */
  .animate-on-scroll {
    @apply opacity-0 translate-y-4 transition-all duration-700 ease-out;
  }
  
  .animate-on-scroll.visible {
    @apply opacity-100 translate-y-0;
  }
}

/* Utility layer for custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* Custom aspect ratios */
  .aspect-food {
    aspect-ratio: 4 / 3;
  }
  
  .aspect-restaurant {
    aspect-ratio: 16 / 9;
  }
}
