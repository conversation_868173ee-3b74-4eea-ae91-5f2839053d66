<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Pizza Palace - Authentic Italian pizzas with fresh ingredients. Order online for fast delivery. View menu, reviews, and restaurant information.">
  <meta name="keywords" content="Pizza Palace, pizza delivery, Italian food, online ordering, restaurant menu">
  <meta name="author" content="Foodie">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://foodie.com/restaurant/pizza-palace">
  <meta property="og:title" content="Pizza Palace - Foodie">
  <meta property="og:description" content="Authentic Italian pizzas with fresh ingredients. Order online for fast delivery.">
  <meta property="og:image" content="https://foodie.com/og-pizza-palace.jpg">

  <title>Pizza Palace - Foodie | Order Online</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/assets/icons/favicon.ico">
  
  <!-- Stylesheets -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
  
  <!-- Custom Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#fef2f2', 100: '#fee2e2', 200: '#fecaca', 300: '#fca5a5', 400: '#f87171',
              500: '#ef4444', 600: '#dc2626', 700: '#b91c1c', 800: '#991b1b', 900: '#7f1d1d'
            },
            secondary: {
              50: '#f8fafc', 100: '#f1f5f9', 200: '#e2e8f0', 300: '#cbd5e1', 400: '#94a3b8',
              500: '#64748b', 600: '#475569', 700: '#334155', 800: '#1e293b', 900: '#0f172a'
            },
            success: {
              50: '#f0fdf4', 100: '#dcfce7', 200: '#bbf7d0', 300: '#86efac', 400: '#4ade80',
              500: '#22c55e', 600: '#16a34a', 700: '#15803d', 800: '#166534', 900: '#14532d'
            },
            warning: {
              50: '#fffbeb', 100: '#fef3c7', 200: '#fde68a', 300: '#fcd34d', 400: '#fbbf24',
              500: '#f59e0b', 600: '#d97706', 700: '#b45309', 800: '#92400e', 900: '#78350f'
            }
          },
          fontFamily: {
            'sans': ['Inter', 'system-ui', 'sans-serif'],
            'display': ['Poppins', 'system-ui', 'sans-serif']
          }
        }
      }
    }
  </script>
  
  <!-- Custom CSS -->
  <style>
    .btn { @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed; }
    .btn-primary { @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-sm hover:shadow-md; }
    .btn-secondary { @apply btn bg-white text-secondary-700 border border-secondary-300 hover:bg-secondary-50 focus:ring-secondary-500 shadow-sm hover:shadow-md; }
    .btn-outline { @apply btn bg-transparent text-primary-600 border border-primary-500 hover:bg-primary-50 focus:ring-primary-500; }
    .btn-sm { @apply px-3 py-1.5 text-xs; }
    .btn-lg { @apply px-6 py-3 text-base; }
    .card { @apply bg-white rounded-xl shadow-sm border border-secondary-100 overflow-hidden; }
    .card-hover { @apply card transition-all duration-300 hover:shadow-lg hover:-translate-y-1; }
    .form-input { @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200; }
    .container-custom { @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8; }
    .bg-gradient-primary { @apply bg-gradient-to-r from-primary-500 to-primary-600; }
    .badge { @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium; }
    .badge-success { @apply badge bg-success-100 text-success-800; }
    .badge-warning { @apply badge bg-warning-100 text-warning-800; }
    .badge-primary { @apply badge bg-primary-100 text-primary-800; }
  </style>
</head>

<body class="font-sans text-secondary-800 antialiased bg-secondary-50">
  <!-- Header -->
  <header class="bg-white shadow-sm border-b border-secondary-100 sticky top-0 z-40" x-data="{ mobileMenuOpen: false }">
    <div class="container-custom">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <a href="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
            <span class="text-xl font-display font-bold text-secondary-900">Foodie</span>
          </a>
        </div>

        <!-- Breadcrumb -->
        <nav class="hidden md:flex items-center space-x-2 text-sm">
          <a href="/" class="text-secondary-600 hover:text-primary-600">Home</a>
          <svg class="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
          <a href="/pages/restaurants.html" class="text-secondary-600 hover:text-primary-600">Restaurants</a>
          <svg class="w-4 h-4 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
          <span class="text-secondary-900 font-medium">Pizza Palace</span>
        </nav>

        <!-- Desktop Actions -->
        <div class="hidden md:flex items-center space-x-4">
          <!-- Cart -->
          <a href="/pages/cart.html" class="relative p-2 text-secondary-600 hover:text-primary-600 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
            </svg>
            <span class="cart-count absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium" style="display: none;">0</span>
          </a>

          <!-- User Menu -->
          <div class="relative" x-data="{ userMenuOpen: false }">
            <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-2 p-2 text-secondary-600 hover:text-primary-600 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </button>
            <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-100 py-2">
              <a href="/pages/login.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Sign In</a>
              <a href="/pages/signup.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Sign Up</a>
              <a href="/pages/account.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">My Account</a>
            </div>
          </div>
        </div>

        <!-- Mobile menu button -->
        <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 text-secondary-600 hover:text-primary-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <div x-show="mobileMenuOpen" x-transition class="md:hidden border-t border-secondary-200 py-4">
        <nav class="space-y-2">
          <a href="/" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Home</a>
          <a href="/pages/restaurants.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Restaurants</a>
          <hr class="my-2 border-secondary-200">
          <a href="/pages/cart.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
            </svg>
            Cart
            <span class="cart-count ml-auto bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium" style="display: none;">0</span>
          </a>
          <a href="/pages/login.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Sign In</a>
          <a href="/pages/signup.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Sign Up</a>
        </nav>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="py-8">
    <div class="container-custom">
      <!-- Restaurant Header -->
      <div class="bg-white rounded-xl shadow-sm border border-secondary-100 overflow-hidden mb-8">
        <div class="relative h-64 md:h-80">
          <img 
            src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" 
            alt="Pizza Palace" 
            class="w-full h-full object-cover"
            loading="eager"
          >
          <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
          
          <!-- Restaurant Info Overlay -->
          <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
            <div class="flex flex-col md:flex-row md:items-end md:justify-between">
              <div>
                <h1 class="text-3xl md:text-4xl font-display font-bold mb-2">Pizza Palace</h1>
                <p class="text-lg mb-2 opacity-90">Italian • Pizza • Fast Food</p>
                <div class="flex items-center space-x-4 text-sm">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 text-warning-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    <span class="font-medium">4.5</span>
                    <span class="opacity-75 ml-1">(1,234 reviews)</span>
                  </div>
                  <span>•</span>
                  <span>₹200 for two</span>
                  <span>•</span>
                  <span>25-30 min</span>
                </div>
              </div>
              
              <div class="mt-4 md:mt-0 flex items-center space-x-3">
                <span class="badge-success">Open</span>
                <span class="badge-primary">50% OFF</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Restaurant Details and Menu -->
      <div class="grid lg:grid-cols-3 gap-8">
        <!-- Menu Section -->
        <div class="lg:col-span-2">
          <!-- Menu Categories -->
          <div class="bg-white rounded-xl shadow-sm border border-secondary-100 p-6 mb-6">
            <h2 class="text-2xl font-display font-bold text-secondary-900 mb-4">Menu</h2>

            <!-- Category Tabs -->
            <div class="flex flex-wrap gap-2 mb-6" x-data="{ activeCategory: 'pizzas' }">
              <button
                @click="activeCategory = 'pizzas'"
                :class="activeCategory === 'pizzas' ? 'bg-primary-500 text-white' : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'"
                class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Pizzas
              </button>
              <button
                @click="activeCategory = 'appetizers'"
                :class="activeCategory === 'appetizers' ? 'bg-primary-500 text-white' : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'"
                class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Appetizers
              </button>
              <button
                @click="activeCategory = 'beverages'"
                :class="activeCategory === 'beverages' ? 'bg-primary-500 text-white' : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200'"
                class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Beverages
              </button>
            </div>

            <!-- Menu Items -->
            <div class="space-y-4">
              <!-- Pizza Items -->
              <div x-show="activeCategory === 'pizzas'" class="space-y-4">
                <!-- Menu Item 1 -->
                <div class="flex flex-col sm:flex-row gap-4 p-4 border border-secondary-200 rounded-lg hover:border-primary-200 transition-colors">
                  <div class="sm:w-24 sm:h-24 flex-shrink-0">
                    <img
                      src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
                      alt="Margherita Pizza"
                      class="w-full h-24 sm:h-full object-cover rounded-lg"
                      loading="lazy"
                    >
                  </div>
                  <div class="flex-1">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                      <div class="flex-1">
                        <h3 class="text-lg font-semibold text-secondary-900 mb-1">Margherita Pizza</h3>
                        <p class="text-sm text-secondary-600 mb-2">Fresh tomatoes, mozzarella cheese, basil leaves, olive oil</p>
                        <div class="flex items-center space-x-2 mb-3">
                          <span class="text-lg font-bold text-secondary-900">₹299</span>
                          <span class="text-sm text-secondary-500 line-through">₹399</span>
                          <span class="badge-success">25% OFF</span>
                        </div>
                      </div>
                      <div class="flex items-center space-x-2 mt-2 sm:mt-0">
                        <button
                          class="btn-primary btn-sm"
                          @click="addToCart({id: 1, name: 'Margherita Pizza', price: 299, image: 'margherita.jpg'})"
                        >
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Menu Item 2 -->
                <div class="flex flex-col sm:flex-row gap-4 p-4 border border-secondary-200 rounded-lg hover:border-primary-200 transition-colors">
                  <div class="sm:w-24 sm:h-24 flex-shrink-0">
                    <img
                      src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
                      alt="Pepperoni Pizza"
                      class="w-full h-24 sm:h-full object-cover rounded-lg"
                      loading="lazy"
                    >
                  </div>
                  <div class="flex-1">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                      <div class="flex-1">
                        <h3 class="text-lg font-semibold text-secondary-900 mb-1">Pepperoni Pizza</h3>
                        <p class="text-sm text-secondary-600 mb-2">Spicy pepperoni, mozzarella cheese, tomato sauce</p>
                        <div class="flex items-center space-x-2 mb-3">
                          <span class="text-lg font-bold text-secondary-900">₹399</span>
                          <span class="badge-warning">Bestseller</span>
                        </div>
                      </div>
                      <div class="flex items-center space-x-2 mt-2 sm:mt-0">
                        <button
                          class="btn-primary btn-sm"
                          @click="addToCart({id: 2, name: 'Pepperoni Pizza', price: 399, image: 'pepperoni.jpg'})"
                        >
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Menu Item 3 -->
                <div class="flex flex-col sm:flex-row gap-4 p-4 border border-secondary-200 rounded-lg hover:border-primary-200 transition-colors">
                  <div class="sm:w-24 sm:h-24 flex-shrink-0">
                    <img
                      src="https://images.unsplash.com/photo-1574071318508-1cdbab80d002?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
                      alt="Veggie Supreme Pizza"
                      class="w-full h-24 sm:h-full object-cover rounded-lg"
                      loading="lazy"
                    >
                  </div>
                  <div class="flex-1">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                      <div class="flex-1">
                        <h3 class="text-lg font-semibold text-secondary-900 mb-1">Veggie Supreme Pizza</h3>
                        <p class="text-sm text-secondary-600 mb-2">Bell peppers, mushrooms, onions, olives, tomatoes, cheese</p>
                        <div class="flex items-center space-x-2 mb-3">
                          <span class="text-lg font-bold text-secondary-900">₹349</span>
                          <span class="badge-success">Veg</span>
                        </div>
                      </div>
                      <div class="flex items-center space-x-2 mt-2 sm:mt-0">
                        <button
                          class="btn-primary btn-sm"
                          @click="addToCart({id: 3, name: 'Veggie Supreme Pizza', price: 349, image: 'veggie.jpg'})"
                        >
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Appetizers -->
              <div x-show="activeCategory === 'appetizers'" class="space-y-4">
                <div class="text-center py-8 text-secondary-500">
                  <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                  <p>Appetizers menu coming soon!</p>
                </div>
              </div>

              <!-- Beverages -->
              <div x-show="activeCategory === 'beverages'" class="space-y-4">
                <div class="text-center py-8 text-secondary-500">
                  <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                  <p>Beverages menu coming soon!</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Restaurant Info -->
          <div class="bg-white rounded-xl shadow-sm border border-secondary-100 p-6">
            <h3 class="text-lg font-semibold text-secondary-900 mb-4">Restaurant Info</h3>

            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-secondary-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                <div>
                  <p class="text-sm font-medium text-secondary-900">Address</p>
                  <p class="text-sm text-secondary-600">123 Pizza Street, Food District, City 12345</p>
                </div>
              </div>

              <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-secondary-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <div>
                  <p class="text-sm font-medium text-secondary-900">Hours</p>
                  <p class="text-sm text-secondary-600">Mon-Sun: 10:00 AM - 11:00 PM</p>
                </div>
              </div>

              <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-secondary-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
                <div>
                  <p class="text-sm font-medium text-secondary-900">Phone</p>
                  <p class="text-sm text-secondary-600">+****************</p>
                </div>
              </div>

              <div class="flex items-start space-x-3">
                <svg class="w-5 h-5 text-secondary-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                </svg>
                <div>
                  <p class="text-sm font-medium text-secondary-900">Average Cost</p>
                  <p class="text-sm text-secondary-600">₹200 for two people</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Offers -->
          <div class="bg-white rounded-xl shadow-sm border border-secondary-100 p-6">
            <h3 class="text-lg font-semibold text-secondary-900 mb-4">Available Offers</h3>

            <div class="space-y-3">
              <div class="flex items-start space-x-3 p-3 bg-primary-50 rounded-lg border border-primary-200">
                <svg class="w-5 h-5 text-primary-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                </svg>
                <div>
                  <p class="text-sm font-medium text-primary-900">50% OFF</p>
                  <p class="text-xs text-primary-700">Up to ₹100 off on orders above ₹200</p>
                </div>
              </div>

              <div class="flex items-start space-x-3 p-3 bg-success-50 rounded-lg border border-success-200">
                <svg class="w-5 h-5 text-success-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                </svg>
                <div>
                  <p class="text-sm font-medium text-success-900">Free Delivery</p>
                  <p class="text-xs text-success-700">On orders above ₹199</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-xl shadow-sm border border-secondary-100 p-6">
            <div class="space-y-3">
              <button class="w-full btn-outline">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                </svg>
                Add to Favorites
              </button>

              <button class="w-full btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                </svg>
                Share Restaurant
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Reviews Section -->
      <div class="bg-white rounded-xl shadow-sm border border-secondary-100 p-6 mt-8">
        <h2 class="text-2xl font-display font-bold text-secondary-900 mb-6">Customer Reviews</h2>

        <!-- Review Summary -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6 p-4 bg-secondary-50 rounded-lg">
          <div class="flex items-center space-x-4">
            <div class="text-center">
              <div class="text-3xl font-bold text-secondary-900">4.5</div>
              <div class="flex items-center justify-center mt-1">
                <svg class="w-4 h-4 text-warning-400 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 text-warning-400 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 text-warning-400 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 text-warning-400 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                <svg class="w-4 h-4 text-secondary-300 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
              <div class="text-sm text-secondary-600 mt-1">1,234 reviews</div>
            </div>
          </div>

          <div class="mt-4 md:mt-0">
            <button class="btn-primary">Write a Review</button>
          </div>
        </div>

        <!-- Individual Reviews -->
        <div class="space-y-6">
          <!-- Review 1 -->
          <div class="border-b border-secondary-200 pb-6">
            <div class="flex items-start space-x-4">
              <img
                src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                alt="Sarah Johnson"
                class="w-10 h-10 rounded-full"
                loading="lazy"
              >
              <div class="flex-1">
                <div class="flex items-center justify-between mb-2">
                  <div>
                    <h4 class="font-semibold text-secondary-900">Sarah Johnson</h4>
                    <div class="flex items-center mt-1">
                      <div class="flex text-warning-400">
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                      </div>
                    </div>
                  </div>
                  <span class="text-sm text-secondary-500">2 days ago</span>
                </div>
                <p class="text-secondary-700 leading-relaxed">
                  Amazing pizza! The Margherita was perfectly cooked with fresh basil and high-quality mozzarella. Delivery was quick and the food arrived hot. Definitely ordering again!
                </p>
              </div>
            </div>
          </div>

          <!-- Review 2 -->
          <div class="border-b border-secondary-200 pb-6">
            <div class="flex items-start space-x-4">
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                alt="Mike Chen"
                class="w-10 h-10 rounded-full"
                loading="lazy"
              >
              <div class="flex-1">
                <div class="flex items-center justify-between mb-2">
                  <div>
                    <h4 class="font-semibold text-secondary-900">Mike Chen</h4>
                    <div class="flex items-center mt-1">
                      <div class="flex text-warning-400">
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                        <svg class="w-4 h-4 text-secondary-300 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                      </div>
                    </div>
                  </div>
                  <span class="text-sm text-secondary-500">1 week ago</span>
                </div>
                <p class="text-secondary-700 leading-relaxed">
                  Great service and delicious food! The pepperoni pizza was loaded with toppings and had the perfect crispy crust. Will definitely be back for more.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center mt-6">
          <button class="btn-secondary">Load More Reviews</button>
        </div>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="bg-secondary-900 text-white">
    <div class="container-custom py-8">
      <div class="text-center">
        <div class="flex items-center justify-center space-x-2 mb-4">
          <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          <span class="text-xl font-display font-bold">Foodie</span>
        </div>
        <p class="text-secondary-400 text-sm mb-4">
          © 2024 Foodie. All rights reserved.
        </p>
        <div class="flex justify-center space-x-6">
          <a href="/" class="text-secondary-400 hover:text-white transition-colors text-sm">Home</a>
          <a href="/pages/restaurants.html" class="text-secondary-400 hover:text-white transition-colors text-sm">Restaurants</a>
          <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
          <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Terms of Service</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // Initialize cart count
    document.addEventListener('DOMContentLoaded', function() {
      updateCartCount();
    });

    // Shopping cart functionality
    let cart = JSON.parse(localStorage.getItem('cart')) || [];

    function updateCartCount() {
      const count = cart.reduce((total, item) => total + item.quantity, 0);
      const cartCountElements = document.querySelectorAll('.cart-count');

      cartCountElements.forEach(element => {
        element.textContent = count;
        element.style.display = count > 0 ? 'inline-flex' : 'none';
      });
    }

    function addToCart(item) {
      const existingItem = cart.find(cartItem => cartItem.id === item.id);

      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        cart.push({ ...item, quantity: 1 });
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      updateCartCount();
      showToast(`${item.name} added to cart!`);
    }

    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500'
      }`;
      toast.textContent = message;

      document.body.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove('translate-x-full');
      }, 100);

      setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 3000);
    }

    // Make addToCart function globally available
    window.addToCart = addToCart;
  </script>
</body>
</html>
