<!-- Header Component -->
<header class="bg-white shadow-sm border-b border-secondary-100 sticky top-0 z-40" x-data="{ mobileMenuOpen: false }">
  <div class="container-custom">
    <div class="flex items-center justify-between h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/" class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          <span class="text-xl font-display font-bold text-secondary-900">Foodie</span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex items-center space-x-8">
        <a href="/" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors">Home</a>
        <a href="/pages/restaurants.html" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors">Restaurants</a>
        <div class="relative" x-data="{ open: false }">
          <button @click="open = !open" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors flex items-center">
            Categories
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div x-show="open" @click.away="open = false" x-transition class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-large border border-secondary-100 py-2">
            <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Pizza</a>
            <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Burgers</a>
            <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Indian</a>
            <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Chinese</a>
            <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Italian</a>
          </div>
        </div>
        <a href="#" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors">About</a>
        <a href="#" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors">Contact</a>
      </nav>

      <!-- Desktop Actions -->
      <div class="hidden md:flex items-center space-x-4">
        <!-- Search -->
        <div class="relative" x-data="{ searchOpen: false }">
          <button @click="searchOpen = !searchOpen" class="p-2 text-secondary-600 hover:text-primary-600 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </button>
          <div x-show="searchOpen" @click.away="searchOpen = false" x-transition class="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-large border border-secondary-100 p-4">
            <input type="text" placeholder="Search restaurants, cuisines..." class="form-input w-full">
          </div>
        </div>

        <!-- Cart -->
        <a href="/pages/cart.html" class="relative p-2 text-secondary-600 hover:text-primary-600 transition-colors">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
          </svg>
          <span class="cart-count absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium" style="display: none;">0</span>
        </a>

        <!-- User Menu -->
        <div class="relative" x-data="{ userMenuOpen: false }">
          <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-2 p-2 text-secondary-600 hover:text-primary-600 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </button>
          <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-large border border-secondary-100 py-2">
            <a href="/pages/login.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Sign In</a>
            <a href="/pages/signup.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Sign Up</a>
            <a href="/pages/account.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">My Account</a>
            <hr class="my-2 border-secondary-200">
            <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Help</a>
          </div>
        </div>
      </div>

      <!-- Mobile menu button -->
      <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 text-secondary-600 hover:text-primary-600 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div x-show="mobileMenuOpen" x-transition class="md:hidden border-t border-secondary-200 py-4">
      <nav class="space-y-2">
        <a href="/" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Home</a>
        <a href="/pages/restaurants.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Restaurants</a>
        <a href="#" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Categories</a>
        <a href="#" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">About</a>
        <a href="#" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Contact</a>
        <hr class="my-2 border-secondary-200">
        <a href="/pages/cart.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
          </svg>
          Cart
          <span class="cart-count ml-auto bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium" style="display: none;">0</span>
        </a>
        <a href="/pages/login.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Sign In</a>
        <a href="/pages/signup.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Sign Up</a>
      </nav>
    </div>
  </div>
</header>
