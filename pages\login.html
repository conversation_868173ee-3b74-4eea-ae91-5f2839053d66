<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Sign in to your Foodie account to order from your favorite restaurants. Fast, secure login with email or social media.">
  <meta name="keywords" content="login, sign in, foodie account, food delivery login">
  <meta name="author" content="Foodie">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://foodie.com/login">
  <meta property="og:title" content="Sign In - Foodie">
  <meta property="og:description" content="Sign in to your Foodie account to order from your favorite restaurants.">
  <meta property="og:image" content="https://foodie.com/og-login.jpg">

  <title>Sign In - Foodie | Login to Your Account</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/assets/icons/favicon.ico">
  
  <!-- Stylesheets -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
  
  <!-- Custom Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#fef2f2', 100: '#fee2e2', 200: '#fecaca', 300: '#fca5a5', 400: '#f87171',
              500: '#ef4444', 600: '#dc2626', 700: '#b91c1c', 800: '#991b1b', 900: '#7f1d1d'
            },
            secondary: {
              50: '#f8fafc', 100: '#f1f5f9', 200: '#e2e8f0', 300: '#cbd5e1', 400: '#94a3b8',
              500: '#64748b', 600: '#475569', 700: '#334155', 800: '#1e293b', 900: '#0f172a'
            },
            success: {
              50: '#f0fdf4', 100: '#dcfce7', 200: '#bbf7d0', 300: '#86efac', 400: '#4ade80',
              500: '#22c55e', 600: '#16a34a', 700: '#15803d', 800: '#166534', 900: '#14532d'
            }
          },
          fontFamily: {
            'sans': ['Inter', 'system-ui', 'sans-serif'],
            'display': ['Poppins', 'system-ui', 'sans-serif']
          }
        }
      }
    }
  </script>
  
  <!-- Custom CSS -->
  <style>
    .btn { @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed; }
    .btn-primary { @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-sm hover:shadow-md; }
    .btn-secondary { @apply btn bg-white text-secondary-700 border border-secondary-300 hover:bg-secondary-50 focus:ring-secondary-500 shadow-sm hover:shadow-md; }
    .btn-lg { @apply px-6 py-3 text-base; }
    .form-input { @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200; }
    .form-label { @apply block text-sm font-medium text-secondary-700 mb-1; }
    .form-error { @apply text-sm text-red-600 mt-1; }
    .bg-gradient-primary { @apply bg-gradient-to-r from-primary-500 to-primary-600; }
  </style>
</head>

<body class="font-sans text-secondary-800 antialiased bg-secondary-50 min-h-screen flex items-center justify-center">
  <!-- Login Container -->
  <div class="w-full max-w-md mx-auto p-6">
    <!-- Back to Home -->
    <div class="text-center mb-8">
      <a href="/" class="inline-flex items-center text-secondary-600 hover:text-primary-600 transition-colors">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Home
      </a>
    </div>

    <!-- Login Card -->
    <div class="bg-white rounded-2xl shadow-lg border border-secondary-100 p-8">
      <!-- Logo and Title -->
      <div class="text-center mb-8">
        <div class="flex items-center justify-center space-x-2 mb-4">
          <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          <span class="text-2xl font-display font-bold text-secondary-900">Foodie</span>
        </div>
        <h1 class="text-2xl font-display font-bold text-secondary-900 mb-2">Welcome back!</h1>
        <p class="text-secondary-600">Sign in to your account to continue</p>
      </div>

      <!-- Login Form -->
      <form class="space-y-6" x-data="loginForm()" @submit.prevent="handleSubmit">
        <!-- Email Field -->
        <div>
          <label for="email" class="form-label">Email address</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <input 
              type="email" 
              id="email" 
              name="email"
              x-model="email"
              placeholder="Enter your email"
              class="form-input pl-10"
              :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': errors.email }"
              required
              autocomplete="email"
            >
          </div>
          <div x-show="errors.email" class="form-error" x-text="errors.email"></div>
        </div>

        <!-- Password Field -->
        <div>
          <label for="password" class="form-label">Password</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              name="password"
              x-model="password"
              placeholder="Enter your password"
              class="form-input pl-10 pr-10"
              :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': errors.password }"
              required
              autocomplete="current-password"
            >
            <button 
              type="button"
              @click="showPassword = !showPassword"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <svg x-show="!showPassword" class="w-5 h-5 text-secondary-400 hover:text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              <svg x-show="showPassword" class="w-5 h-5 text-secondary-400 hover:text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
            </button>
          </div>
          <div x-show="errors.password" class="form-error" x-text="errors.password"></div>
        </div>

        <!-- Remember Me and Forgot Password -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input 
              type="checkbox" 
              id="remember" 
              name="remember"
              x-model="remember"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
            >
            <label for="remember" class="ml-2 block text-sm text-secondary-700">
              Remember me
            </label>
          </div>
          <a href="#" class="text-sm text-primary-600 hover:text-primary-500 font-medium">
            Forgot password?
          </a>
        </div>

        <!-- Submit Button -->
        <button 
          type="submit" 
          class="btn-primary btn-lg w-full"
          :disabled="loading"
          :class="{ 'opacity-50 cursor-not-allowed': loading }"
        >
          <svg x-show="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span x-text="loading ? 'Signing in...' : 'Sign in'"></span>
        </button>

        <!-- Divider -->
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-secondary-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-secondary-500">Or continue with</span>
          </div>
        </div>

        <!-- Social Login -->
        <div class="grid grid-cols-2 gap-3">
          <button type="button" class="btn-secondary">
            <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Google
          </button>
          <button type="button" class="btn-secondary">
            <svg class="w-5 h-5 mr-2" fill="#1877F2" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
            Facebook
          </button>
        </div>
      </form>

      <!-- Sign Up Link -->
      <div class="text-center mt-6">
        <p class="text-sm text-secondary-600">
          Don't have an account? 
          <a href="/pages/signup.html" class="font-medium text-primary-600 hover:text-primary-500">
            Sign up for free
          </a>
        </p>
      </div>
    </div>
  </div>

  <!-- JavaScript -->
  <script>
    function loginForm() {
      return {
        email: '',
        password: '',
        remember: false,
        showPassword: false,
        loading: false,
        errors: {},

        handleSubmit() {
          this.errors = {};
          this.loading = true;

          // Basic validation
          if (!this.email) {
            this.errors.email = 'Email is required';
          } else if (!this.isValidEmail(this.email)) {
            this.errors.email = 'Please enter a valid email address';
          }

          if (!this.password) {
            this.errors.password = 'Password is required';
          } else if (this.password.length < 6) {
            this.errors.password = 'Password must be at least 6 characters';
          }

          // If there are errors, stop loading
          if (Object.keys(this.errors).length > 0) {
            this.loading = false;
            return;
          }

          // Simulate API call
          setTimeout(() => {
            this.loading = false;
            // In a real app, this would make an API call
            this.showToast('Login successful! Redirecting...', 'success');
            
            // Redirect to home page after successful login
            setTimeout(() => {
              window.location.href = '/';
            }, 1500);
          }, 2000);
        },

        isValidEmail(email) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          return emailRegex.test(email);
        },

        showToast(message, type = 'success') {
          const toast = document.createElement('div');
          toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300 ${
            type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500'
          }`;
          toast.textContent = message;
          
          document.body.appendChild(toast);
          
          setTimeout(() => {
            toast.classList.remove('translate-x-full');
          }, 100);
          
          setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
              document.body.removeChild(toast);
            }, 300);
          }, 3000);
        }
      }
    }
  </script>
</body>
</html>
