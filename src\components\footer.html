<!-- Footer Component -->
<footer class="bg-secondary-900 text-white">
  <div class="container-custom py-12">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <!-- Company Info -->
      <div class="space-y-4">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          <span class="text-xl font-display font-bold">Foodie</span>
        </div>
        <p class="text-secondary-300 text-sm leading-relaxed">
          Discover and order from the best restaurants in your city. Fast delivery, great food, amazing experience.
        </p>
        <div class="flex space-x-4">
          <a href="#" class="text-secondary-400 hover:text-white transition-colors" aria-label="Facebook">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clip-rule="evenodd"/>
            </svg>
          </a>
          <a href="#" class="text-secondary-400 hover:text-white transition-colors" aria-label="Twitter">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"/>
            </svg>
          </a>
          <a href="#" class="text-secondary-400 hover:text-white transition-colors" aria-label="Instagram">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"/>
            </svg>
          </a>
          <a href="#" class="text-secondary-400 hover:text-white transition-colors" aria-label="LinkedIn">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"/>
            </svg>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="/" class="text-secondary-300 hover:text-white transition-colors text-sm">Home</a></li>
          <li><a href="/pages/restaurants.html" class="text-secondary-300 hover:text-white transition-colors text-sm">Restaurants</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">About Us</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Contact</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Careers</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Blog</a></li>
        </ul>
      </div>

      <!-- Categories -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">Popular Cuisines</h3>
        <ul class="space-y-2">
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Pizza</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Burgers</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Indian</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Chinese</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Italian</a></li>
          <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Mexican</a></li>
        </ul>
      </div>

      <!-- Contact Info -->
      <div class="space-y-4">
        <h3 class="text-lg font-semibold">Get in Touch</h3>
        <div class="space-y-3">
          <div class="flex items-start space-x-3">
            <svg class="w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            <p class="text-secondary-300 text-sm">123 Food Street, Gourmet City, FC 12345</p>
          </div>
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
            </svg>
            <p class="text-secondary-300 text-sm">+1 (555) 123-4567</p>
          </div>
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
            <p class="text-secondary-300 text-sm"><EMAIL></p>
          </div>
        </div>

        <!-- App Download -->
        <div class="pt-4">
          <p class="text-sm font-medium mb-3">Download Our App</p>
          <div class="flex space-x-3">
            <a href="#" class="inline-block">
              <img src="https://via.placeholder.com/120x40/000000/FFFFFF?text=App+Store" alt="Download on App Store" class="h-10 rounded">
            </a>
            <a href="#" class="inline-block">
              <img src="https://via.placeholder.com/120x40/000000/FFFFFF?text=Google+Play" alt="Get it on Google Play" class="h-10 rounded">
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Section -->
    <div class="border-t border-secondary-800 mt-12 pt-8">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        <p class="text-secondary-400 text-sm">
          © 2024 Foodie. All rights reserved.
        </p>
        <div class="flex space-x-6">
          <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
          <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Terms of Service</a>
          <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Cookie Policy</a>
        </div>
      </div>
    </div>
  </div>
</footer>
