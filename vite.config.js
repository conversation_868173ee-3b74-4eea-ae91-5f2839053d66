import { defineConfig } from 'vite'

export default defineConfig({
  root: '.',
  build: {
    outDir: 'dist',
    rollupOptions: {
      input: {
        main: 'index.html',
        restaurants: 'pages/restaurants.html',
        restaurant: 'pages/restaurant.html',
        login: 'pages/login.html',
        signup: 'pages/signup.html',
        account: 'pages/account.html',
        cart: 'pages/cart.html',
        checkout: 'pages/checkout.html'
      }
    }
  },
  server: {
    port: 3000,
    open: true
  }
})
