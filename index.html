<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Discover and order from the best restaurants in your city. Fast delivery, great food, amazing experience with Foodie.">
  <meta name="keywords" content="food delivery, restaurants, online ordering, fast delivery, food app">
  <meta name="author" content="Foodie">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://foodie.com/">
  <meta property="og:title" content="Foodie - Best Food Delivery Service">
  <meta property="og:description" content="Discover and order from the best restaurants in your city. Fast delivery, great food, amazing experience.">
  <meta property="og:image" content="https://foodie.com/og-image.jpg">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://foodie.com/">
  <meta property="twitter:title" content="Foodie - Best Food Delivery Service">
  <meta property="twitter:description" content="Discover and order from the best restaurants in your city. Fast delivery, great food, amazing experience.">
  <meta property="twitter:image" content="https://foodie.com/og-image.jpg">

  <title>Foodie - Best Food Delivery Service | Order Online</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/assets/icons/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="/assets/icons/apple-touch-icon.png">
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" as="style">
  
  <!-- Stylesheets -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
  
  <!-- Custom Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#fef2f2', 100: '#fee2e2', 200: '#fecaca', 300: '#fca5a5', 400: '#f87171',
              500: '#ef4444', 600: '#dc2626', 700: '#b91c1c', 800: '#991b1b', 900: '#7f1d1d'
            },
            secondary: {
              50: '#f8fafc', 100: '#f1f5f9', 200: '#e2e8f0', 300: '#cbd5e1', 400: '#94a3b8',
              500: '#64748b', 600: '#475569', 700: '#334155', 800: '#1e293b', 900: '#0f172a'
            },
            success: {
              50: '#f0fdf4', 100: '#dcfce7', 200: '#bbf7d0', 300: '#86efac', 400: '#4ade80',
              500: '#22c55e', 600: '#16a34a', 700: '#15803d', 800: '#166534', 900: '#14532d'
            },
            warning: {
              50: '#fffbeb', 100: '#fef3c7', 200: '#fde68a', 300: '#fcd34d', 400: '#fbbf24',
              500: '#f59e0b', 600: '#d97706', 700: '#b45309', 800: '#92400e', 900: '#78350f'
            }
          },
          fontFamily: {
            'sans': ['Inter', 'system-ui', 'sans-serif'],
            'display': ['Poppins', 'system-ui', 'sans-serif']
          },
          animation: {
            'fade-in': 'fadeIn 0.5s ease-in-out',
            'slide-up': 'slideUp 0.5s ease-out',
            'bounce-gentle': 'bounceGentle 0.6s ease-out'
          },
          keyframes: {
            fadeIn: { '0%': { opacity: '0' }, '100%': { opacity: '1' } },
            slideUp: { '0%': { transform: 'translateY(20px)', opacity: '0' }, '100%': { transform: 'translateY(0)', opacity: '1' } },
            bounceGentle: { '0%, 20%, 50%, 80%, 100%': { transform: 'translateY(0)' }, '40%': { transform: 'translateY(-4px)' }, '60%': { transform: 'translateY(-2px)' } }
          }
        }
      }
    }
  </script>
  
  <!-- Custom CSS -->
  <style>
    .btn { @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed; }
    .btn-primary { @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-sm hover:shadow-md; }
    .btn-secondary { @apply btn bg-white text-secondary-700 border border-secondary-300 hover:bg-secondary-50 focus:ring-secondary-500 shadow-sm hover:shadow-md; }
    .btn-lg { @apply px-6 py-3 text-base; }
    .card { @apply bg-white rounded-xl shadow-sm border border-secondary-100 overflow-hidden; }
    .card-hover { @apply card transition-all duration-300 hover:shadow-lg hover:-translate-y-1; }
    .form-input { @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200; }
    .container-custom { @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8; }
    .text-gradient { @apply bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent; }
    .bg-gradient-primary { @apply bg-gradient-to-r from-primary-500 to-primary-600; }
    .animate-on-scroll { @apply opacity-0 translate-y-4 transition-all duration-700 ease-out; }
    .animate-on-scroll.visible { @apply opacity-100 translate-y-0; }
  </style>
</head>

<body class="font-sans text-secondary-800 antialiased">
  <!-- Header -->
  <header class="bg-white shadow-sm border-b border-secondary-100 sticky top-0 z-40" x-data="{ mobileMenuOpen: false }">
    <div class="container-custom">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <a href="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
            <span class="text-xl font-display font-bold text-secondary-900">Foodie</span>
          </a>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <a href="/" class="text-primary-600 font-medium">Home</a>
          <a href="/pages/restaurants.html" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors">Restaurants</a>
          <div class="relative" x-data="{ open: false }">
            <button @click="open = !open" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors flex items-center">
              Categories
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div x-show="open" @click.away="open = false" x-transition class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-100 py-2">
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Pizza</a>
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Burgers</a>
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Indian</a>
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Chinese</a>
              <a href="#" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Italian</a>
            </div>
          </div>
          <a href="#" class="text-secondary-700 hover:text-primary-600 font-medium transition-colors">About</a>
        </nav>

        <!-- Desktop Actions -->
        <div class="hidden md:flex items-center space-x-4">
          <!-- Cart -->
          <a href="/pages/cart.html" class="relative p-2 text-secondary-600 hover:text-primary-600 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
            </svg>
            <span class="cart-count absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium" style="display: none;">0</span>
          </a>

          <!-- User Menu -->
          <div class="relative" x-data="{ userMenuOpen: false }">
            <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-2 p-2 text-secondary-600 hover:text-primary-600 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </button>
            <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-100 py-2">
              <a href="/pages/login.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Sign In</a>
              <a href="/pages/signup.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">Sign Up</a>
              <a href="/pages/account.html" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50">My Account</a>
            </div>
          </div>
        </div>

        <!-- Mobile menu button -->
        <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 text-secondary-600 hover:text-primary-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <div x-show="mobileMenuOpen" x-transition class="md:hidden border-t border-secondary-200 py-4">
        <nav class="space-y-2">
          <a href="/" class="block px-4 py-2 text-primary-600 bg-primary-50 rounded-lg font-medium">Home</a>
          <a href="/pages/restaurants.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Restaurants</a>
          <a href="#" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Categories</a>
          <a href="#" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">About</a>
          <hr class="my-2 border-secondary-200">
          <a href="/pages/cart.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
            </svg>
            Cart
            <span class="cart-count ml-auto bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium" style="display: none;">0</span>
          </a>
          <a href="/pages/login.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Sign In</a>
          <a href="/pages/signup.html" class="block px-4 py-2 text-secondary-700 hover:bg-secondary-50 rounded-lg">Sign Up</a>
        </nav>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-primary-50 via-white to-secondary-50 overflow-hidden">
      <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ef4444" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>
      
      <div class="container-custom relative">
        <div class="py-20 lg:py-32">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <!-- Hero Content -->
            <div class="animate-on-scroll">
              <h1 class="text-4xl lg:text-6xl font-display font-bold text-secondary-900 leading-tight mb-6">
                Delicious Food
                <span class="text-gradient block">Delivered Fast</span>
              </h1>
              <p class="text-lg text-secondary-600 mb-8 leading-relaxed">
                Discover amazing restaurants in your city and get your favorite meals delivered right to your doorstep. Fast, fresh, and always delicious.
              </p>
              
              <!-- Search Bar -->
              <div class="bg-white rounded-2xl shadow-lg p-2 mb-8 max-w-lg" x-data="{ location: '', searchQuery: '' }">
                <div class="flex flex-col sm:flex-row gap-2">
                  <div class="flex-1 relative">
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input 
                      type="text" 
                      x-model="searchQuery"
                      placeholder="Search for restaurants or cuisines..." 
                      class="w-full pl-10 pr-4 py-3 border-0 focus:ring-0 text-secondary-700 placeholder-secondary-400"
                    >
                  </div>
                  <div class="flex-shrink-0 relative">
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <input 
                      type="text" 
                      x-model="location"
                      placeholder="Enter location" 
                      class="w-full sm:w-40 pl-10 pr-4 py-3 border-0 focus:ring-0 text-secondary-700 placeholder-secondary-400"
                    >
                  </div>
                  <button class="btn-primary btn-lg px-8 whitespace-nowrap">
                    Find Food
                  </button>
                </div>
              </div>

              <!-- Stats -->
              <div class="flex flex-wrap gap-8">
                <div class="text-center">
                  <div class="text-2xl font-bold text-secondary-900">1000+</div>
                  <div class="text-sm text-secondary-600">Restaurants</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-secondary-900">50K+</div>
                  <div class="text-sm text-secondary-600">Happy Customers</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-secondary-900">30min</div>
                  <div class="text-sm text-secondary-600">Avg Delivery</div>
                </div>
              </div>
            </div>

            <!-- Hero Image -->
            <div class="animate-on-scroll lg:animate-bounce-gentle">
              <div class="relative">
                <img 
                  src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
                  alt="Delicious food delivery" 
                  class="w-full h-auto rounded-2xl shadow-2xl"
                  loading="eager"
                >
                <div class="absolute -bottom-6 -left-6 bg-white rounded-xl shadow-lg p-4 animate-bounce-gentle">
                  <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-success-100 rounded-full flex items-center justify-center">
                      <svg class="w-6 h-6 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <div>
                      <div class="font-semibold text-secondary-900">Order Confirmed!</div>
                      <div class="text-sm text-secondary-600">Arriving in 25 mins</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-white">
      <div class="container-custom">
        <div class="text-center mb-12 animate-on-scroll">
          <h2 class="text-3xl lg:text-4xl font-display font-bold text-secondary-900 mb-4">
            What's on your mind?
          </h2>
          <p class="text-lg text-secondary-600 max-w-2xl mx-auto">
            Explore our wide variety of cuisines and find your next favorite meal
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
          <!-- Category Items -->
          <div class="animate-on-scroll group cursor-pointer">
            <div class="bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl p-6 text-center transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
              <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-sm">
                <span class="text-2xl">🍕</span>
              </div>
              <h3 class="font-semibold text-secondary-900">Pizza</h3>
            </div>
          </div>

          <div class="animate-on-scroll group cursor-pointer">
            <div class="bg-gradient-to-br from-red-100 to-red-200 rounded-2xl p-6 text-center transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
              <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-sm">
                <span class="text-2xl">🍔</span>
              </div>
              <h3 class="font-semibold text-secondary-900">Burgers</h3>
            </div>
          </div>

          <div class="animate-on-scroll group cursor-pointer">
            <div class="bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-2xl p-6 text-center transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
              <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-sm">
                <span class="text-2xl">🍛</span>
              </div>
              <h3 class="font-semibold text-secondary-900">Indian</h3>
            </div>
          </div>

          <div class="animate-on-scroll group cursor-pointer">
            <div class="bg-gradient-to-br from-green-100 to-green-200 rounded-2xl p-6 text-center transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
              <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-sm">
                <span class="text-2xl">🍜</span>
              </div>
              <h3 class="font-semibold text-secondary-900">Chinese</h3>
            </div>
          </div>

          <div class="animate-on-scroll group cursor-pointer">
            <div class="bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl p-6 text-center transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
              <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-sm">
                <span class="text-2xl">🍝</span>
              </div>
              <h3 class="font-semibold text-secondary-900">Italian</h3>
            </div>
          </div>

          <div class="animate-on-scroll group cursor-pointer">
            <div class="bg-gradient-to-br from-pink-100 to-pink-200 rounded-2xl p-6 text-center transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
              <div class="w-16 h-16 mx-auto mb-3 bg-white rounded-full flex items-center justify-center shadow-sm">
                <span class="text-2xl">🌮</span>
              </div>
              <h3 class="font-semibold text-secondary-900">Mexican</h3>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Restaurants Section -->
    <section class="py-16 bg-secondary-50">
      <div class="container-custom">
        <div class="flex justify-between items-center mb-12 animate-on-scroll">
          <div>
            <h2 class="text-3xl lg:text-4xl font-display font-bold text-secondary-900 mb-2">
              Popular Restaurants
            </h2>
            <p class="text-lg text-secondary-600">
              Top-rated restaurants in your area
            </p>
          </div>
          <a href="/pages/restaurants.html" class="btn-secondary hidden sm:inline-flex">
            View All
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Restaurant Card 1 -->
          <div class="card-hover animate-on-scroll">
            <div class="relative">
              <img
                src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                alt="Pizza Palace"
                class="w-full h-48 object-cover"
                loading="lazy"
              >
              <div class="absolute top-4 left-4">
                <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  ⭐ 4.5
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                  25-30 min
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-secondary-900 mb-2">Pizza Palace</h3>
              <p class="text-secondary-600 mb-3">Italian • Pizza • Fast Food</p>
              <div class="flex items-center justify-between">
                <span class="text-sm text-secondary-500">₹200 for two</span>
                <button class="btn-primary btn-sm">Order Now</button>
              </div>
            </div>
          </div>

          <!-- Restaurant Card 2 -->
          <div class="card-hover animate-on-scroll">
            <div class="relative">
              <img
                src="https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                alt="Burger Barn"
                class="w-full h-48 object-cover"
                loading="lazy"
              >
              <div class="absolute top-4 left-4">
                <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  ⭐ 4.2
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                  20-25 min
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-secondary-900 mb-2">Burger Barn</h3>
              <p class="text-secondary-600 mb-3">American • Burgers • Fast Food</p>
              <div class="flex items-center justify-between">
                <span class="text-sm text-secondary-500">₹300 for two</span>
                <button class="btn-primary btn-sm">Order Now</button>
              </div>
            </div>
          </div>

          <!-- Restaurant Card 3 -->
          <div class="card-hover animate-on-scroll">
            <div class="relative">
              <img
                src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                alt="Spice Garden"
                class="w-full h-48 object-cover"
                loading="lazy"
              >
              <div class="absolute top-4 left-4">
                <span class="bg-success-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  ⭐ 4.7
                </span>
              </div>
              <div class="absolute top-4 right-4">
                <span class="bg-white text-secondary-700 px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                  35-40 min
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-secondary-900 mb-2">Spice Garden</h3>
              <p class="text-secondary-600 mb-3">Indian • North Indian • Biryani</p>
              <div class="flex items-center justify-between">
                <span class="text-sm text-secondary-500">₹400 for two</span>
                <button class="btn-primary btn-sm">Order Now</button>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center mt-8 sm:hidden">
          <a href="/pages/restaurants.html" class="btn-secondary">
            View All Restaurants
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-16 bg-white">
      <div class="container-custom">
        <div class="text-center mb-12 animate-on-scroll">
          <h2 class="text-3xl lg:text-4xl font-display font-bold text-secondary-900 mb-4">
            How It Works
          </h2>
          <p class="text-lg text-secondary-600 max-w-2xl mx-auto">
            Getting your favorite food delivered is easier than ever
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <!-- Step 1 -->
          <div class="text-center animate-on-scroll">
            <div class="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-secondary-900 mb-3">1. Choose Restaurant</h3>
            <p class="text-secondary-600 leading-relaxed">
              Browse through hundreds of restaurants and find your favorite cuisine or discover something new.
            </p>
          </div>

          <!-- Step 2 -->
          <div class="text-center animate-on-scroll">
            <div class="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-secondary-900 mb-3">2. Place Order</h3>
            <p class="text-secondary-600 leading-relaxed">
              Select your favorite dishes, customize them to your taste, and add them to your cart.
            </p>
          </div>

          <!-- Step 3 -->
          <div class="text-center animate-on-scroll">
            <div class="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-secondary-900 mb-3">3. Fast Delivery</h3>
            <p class="text-secondary-600 leading-relaxed">
              Sit back and relax while our delivery partners bring your delicious meal right to your door.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Promotional Banner -->
    <section class="py-16 bg-gradient-to-r from-primary-500 to-primary-600 text-white">
      <div class="container-custom">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <div class="animate-on-scroll">
            <h2 class="text-3xl lg:text-4xl font-display font-bold mb-4">
              Get 50% Off Your First Order!
            </h2>
            <p class="text-lg text-primary-100 mb-6 leading-relaxed">
              New to Foodie? Use code <strong>WELCOME50</strong> and enjoy half price on your first order. Valid for orders above ₹300.
            </p>
            <div class="flex flex-col sm:flex-row gap-4">
              <button class="btn bg-white text-primary-600 hover:bg-primary-50 btn-lg">
                Order Now
              </button>
              <button class="btn bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 btn-lg">
                Learn More
              </button>
            </div>
          </div>
          <div class="animate-on-scroll">
            <img
              src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
              alt="Special offer"
              class="w-full h-auto rounded-2xl shadow-2xl"
              loading="lazy"
            >
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-16 bg-secondary-50">
      <div class="container-custom">
        <div class="text-center mb-12 animate-on-scroll">
          <h2 class="text-3xl lg:text-4xl font-display font-bold text-secondary-900 mb-4">
            What Our Customers Say
          </h2>
          <p class="text-lg text-secondary-600 max-w-2xl mx-auto">
            Don't just take our word for it - hear from our satisfied customers
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <!-- Testimonial 1 -->
          <div class="card animate-on-scroll">
            <div class="p-6">
              <div class="flex items-center mb-4">
                <div class="flex text-warning-400">
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                </div>
              </div>
              <p class="text-secondary-700 mb-4 leading-relaxed">
                "Amazing service! The food arrived hot and fresh, exactly as ordered. The delivery was super fast and the app is so easy to use."
              </p>
              <div class="flex items-center">
                <img
                  src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                  alt="Sarah Johnson"
                  class="w-10 h-10 rounded-full mr-3"
                  loading="lazy"
                >
                <div>
                  <div class="font-semibold text-secondary-900">Sarah Johnson</div>
                  <div class="text-sm text-secondary-600">Regular Customer</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Testimonial 2 -->
          <div class="card animate-on-scroll">
            <div class="p-6">
              <div class="flex items-center mb-4">
                <div class="flex text-warning-400">
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                </div>
              </div>
              <p class="text-secondary-700 mb-4 leading-relaxed">
                "Best food delivery app I've used! Great variety of restaurants and the tracking feature is fantastic. Highly recommended!"
              </p>
              <div class="flex items-center">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                  alt="Mike Chen"
                  class="w-10 h-10 rounded-full mr-3"
                  loading="lazy"
                >
                <div>
                  <div class="font-semibold text-secondary-900">Mike Chen</div>
                  <div class="text-sm text-secondary-600">Food Enthusiast</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Testimonial 3 -->
          <div class="card animate-on-scroll">
            <div class="p-6">
              <div class="flex items-center mb-4">
                <div class="flex text-warning-400">
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                </div>
              </div>
              <p class="text-secondary-700 mb-4 leading-relaxed">
                "Foodie has become my go-to app for ordering food. The customer service is excellent and they always resolve any issues quickly."
              </p>
              <div class="flex items-center">
                <img
                  src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
                  alt="Emily Davis"
                  class="w-10 h-10 rounded-full mr-3"
                  loading="lazy"
                >
                <div>
                  <div class="font-semibold text-secondary-900">Emily Davis</div>
                  <div class="text-sm text-secondary-600">Busy Professional</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="bg-secondary-900 text-white">
    <div class="container-custom py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
            <span class="text-xl font-display font-bold">Foodie</span>
          </div>
          <p class="text-secondary-300 text-sm leading-relaxed">
            Discover and order from the best restaurants in your city. Fast delivery, great food, amazing experience.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-secondary-400 hover:text-white transition-colors" aria-label="Facebook">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clip-rule="evenodd"/>
              </svg>
            </a>
            <a href="#" class="text-secondary-400 hover:text-white transition-colors" aria-label="Twitter">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"/>
              </svg>
            </a>
            <a href="#" class="text-secondary-400 hover:text-white transition-colors" aria-label="Instagram">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Quick Links</h3>
          <ul class="space-y-2">
            <li><a href="/" class="text-secondary-300 hover:text-white transition-colors text-sm">Home</a></li>
            <li><a href="/pages/restaurants.html" class="text-secondary-300 hover:text-white transition-colors text-sm">Restaurants</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">About Us</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Contact</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Careers</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Blog</a></li>
          </ul>
        </div>

        <!-- Categories -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Popular Cuisines</h3>
          <ul class="space-y-2">
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Pizza</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Burgers</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Indian</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Chinese</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Italian</a></li>
            <li><a href="#" class="text-secondary-300 hover:text-white transition-colors text-sm">Mexican</a></li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Get in Touch</h3>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <svg class="w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              <p class="text-secondary-300 text-sm">123 Food Street, Gourmet City, FC 12345</p>
            </div>
            <div class="flex items-center space-x-3">
              <svg class="w-5 h-5 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
              </svg>
              <p class="text-secondary-300 text-sm">+1 (555) 123-4567</p>
            </div>
            <div class="flex items-center space-x-3">
              <svg class="w-5 h-5 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
              <p class="text-secondary-300 text-sm"><EMAIL></p>
            </div>
          </div>

          <!-- App Download -->
          <div class="pt-4">
            <p class="text-sm font-medium mb-3">Download Our App</p>
            <div class="flex space-x-3">
              <a href="#" class="inline-block">
                <img src="https://via.placeholder.com/120x40/000000/FFFFFF?text=App+Store" alt="Download on App Store" class="h-10 rounded">
              </a>
              <a href="#" class="inline-block">
                <img src="https://via.placeholder.com/120x40/000000/FFFFFF?text=Google+Play" alt="Get it on Google Play" class="h-10 rounded">
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="border-t border-secondary-800 mt-12 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p class="text-secondary-400 text-sm">
            © 2024 Foodie. All rights reserved.
          </p>
          <div class="flex space-x-6">
            <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
            <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Terms of Service</a>
            <a href="#" class="text-secondary-400 hover:text-white transition-colors text-sm">Cookie Policy</a>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    // Initialize scroll animations
    document.addEventListener('DOMContentLoaded', function() {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      }, { threshold: 0.1, rootMargin: '0px 0px -50px 0px' });

      document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
      });

      // Initialize cart count
      updateCartCount();
    });

    // Shopping cart functionality
    let cart = JSON.parse(localStorage.getItem('cart')) || [];

    function updateCartCount() {
      const count = cart.reduce((total, item) => total + item.quantity, 0);
      const cartCountElements = document.querySelectorAll('.cart-count');

      cartCountElements.forEach(element => {
        element.textContent = count;
        element.style.display = count > 0 ? 'inline-flex' : 'none';
      });
    }

    function addToCart(item) {
      const existingItem = cart.find(cartItem => cartItem.id === item.id);

      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        cart.push({ ...item, quantity: 1 });
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      updateCartCount();
      showToast(`${item.name} added to cart!`);
    }

    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500'
      }`;
      toast.textContent = message;

      document.body.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove('translate-x-full');
      }, 100);

      setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 3000);
    }
  </script>
</body>
</html>
